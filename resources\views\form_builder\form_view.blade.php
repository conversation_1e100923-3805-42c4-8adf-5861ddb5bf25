@php
    $logo=\App\Models\Utility::get_file('uploads/logo');
    $company_favicon=Utility::getValByName('company_favicon');
    $favicon=Utility::getValByName('company_favicon');

    $getseo= App\Models\Utility::getSeoSetting();
    $metatitle =  isset($getseo['meta_title']) ? $getseo['meta_title'] :'';
    $metsdesc= isset($getseo['meta_desc'])?$getseo['meta_desc']:'';
    $meta_image = \App\Models\Utility::get_file('uploads/meta/');
    $meta_logo = isset($getseo['meta_image'])?$getseo['meta_image']:'';
    $get_cookie = \App\Models\Utility::getCookieSetting();

    $setting = App\Models\Utility::settingsById($form->created_by);
    $color = !empty($setting['color']) ? $setting['color'] : 'theme-3';
    $company_logo = isset($setting['company_logo_dark']) ? $setting['company_logo_dark'] : 'logo-dark.png';

if(isset($setting['color_flag']) && $setting['color_flag'] == 'true')
{
    $themeColor = 'custom-color';
}
else {
    $themeColor = $color;
}
@endphp

<html lang="en">
<meta name="csrf-token" id="csrf-token" content="{{ csrf_token() }}">
<head>
    <title>{{(Utility::getValByName('title_text')) ? Utility::getValByName('title_text') : config('app.name', 'ERPGO')}} - Form Builder</title>

    <meta name="title" content="{{$metatitle}}">
    <meta name="description" content="{{$metsdesc}}">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ env('APP_URL') }}">
    <meta property="og:title" content="{{$metatitle}}">
    <meta property="og:description" content="{{$metsdesc}}">
    <meta property="og:image" content="{{$meta_image.$meta_logo}}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ env('APP_URL') }}">
    <meta property="twitter:title" content="{{$metatitle}}">
    <meta property="twitter:description" content="{{$metsdesc}}">
    <meta property="twitter:image" content="{{$meta_image.$meta_logo}}">


    <!-- Meta -->
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <link rel="icon" href="{{$logo.'/'.(isset($company_favicon) && !empty($company_favicon)?$company_favicon:'favicon.png')}}" type="image" sizes="16x16">

    <!-- Favicon icon -->
    <link rel="icon" href="{{ asset('assets/images/favicon.svg') }}" type="image/x-icon"/>

    <link rel="stylesheet" href="{{ asset('assets/css/plugins/style.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/plugins/animate.min.css') }}">

    <!-- font css -->
    <link rel="stylesheet" href="{{ asset('assets/fonts/tabler-icons.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/feather.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/fontawesome.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/material.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/plugins/main.css') }}">
    <!-- vendor css -->
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}" id="main-style-link">
    <link rel="stylesheet" href="{{ asset('assets/css/customizer.css') }}">

    <link rel="stylesheet" href="{{ asset('css/custom.css') }}" id="main-style-link">

    <style>
        :root {
            --color-customColor: <?= $color ?>;
        }

        .form-label.required {
            font-weight: 600;
        }

        .text-danger {
            margin-left: 3px;
        }

        .form-control:required {
            border-left: 3px solid #dc3545;
        }

        .form-check-input:required {
            border-color: #dc3545;
        }

        .card-body.overflow_hidden {
            overflow: hidden !important;
        }

        .card {
            overflow: hidden !important;
        }

        /* Country Code Dropdown Styling */
        .country-code-select {
            border-right: none !important;
            border-top-right-radius: 0 !important;
            border-bottom-right-radius: 0 !important;
            background-color: #f8f9fa !important;
            font-size: 14px !important;
            padding: 0.375rem 0.5rem !important;
        }

        .country-code-select + .form-control {
            border-left: none !important;
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
        }

        .input-group .country-code-select:focus {
            box-shadow: none !important;
            border-color: #80bdff !important;
        }

        .input-group .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

        /* Phone Input Container Styling */
        .phone-input-container {
            position: relative;
            display: flex;
            align-items: stretch;
            width: 100%;
        }

        .country-selector {
            position: relative;
            z-index: 10;
        }

        .country-dropdown-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 0.375rem 0.75rem;
            background-color: #f8f9fa;
            border: 1px solid {{ $customStyles['borderColor'] ?? '#ced4da' }};
            border-right: none;
            border-radius: 0.375rem 0 0 0.375rem;
            color: #495057;
            font-size: 14px;
            line-height: 1.5;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
            min-width: 80px;
            justify-content: center;
            height: calc(1.5em + 0.75rem + 2px);
            box-sizing: border-box;
        }

        .country-dropdown-btn:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
        }

        .country-dropdown-btn:focus {
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            border-color: #80bdff;
        }

        .country-dropdown-btn .flag {
            font-size: 16px;
        }

        .country-dropdown-btn .code {
            font-weight: 500;
            font-size: 13px;
        }

        .country-dropdown-btn .ti-chevron-down {
            font-size: 12px;
            opacity: 0.7;
        }

        .phone-number-input {
            border-left: none !important;
            border-radius: 0 0.375rem 0.375rem 0 !important;
            flex: 1;
        }

        .phone-number-input:focus {
            border-color: #80bdff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

        .country-dropdown {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .country-dropdown .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.15s ease-in-out;
        }

        .country-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .country-dropdown .dropdown-item:active {
            background-color: #007bff;
            color: white;
        }

        @php
            $customStyles = null;
            if ($form && $form->form_styles) {
                $customStyles = json_decode($form->form_styles, true);
            }
        @endphp

        @if($customStyles)
        /* Custom Form Styles */
        @php
            $bgColor = $customStyles['backgroundColor'] ?? '#ffffff';
            $isTransparent = $customStyles['backgroundTransparent'] ?? false;

            if($isTransparent) {
                $finalBgColor = 'transparent';
                $boxShadow = 'none';
                $borderStyle = 'none';
            } else {
                $finalBgColor = $bgColor;
                $boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                $borderStyle = '1px solid ' . ($customStyles['borderColor'] ?? '#ced4da');
            }
        @endphp

        .card, .card-body {
            background-color: {{ $finalBgColor }} !important;
            box-shadow: {{ $boxShadow }} !important;
            font-family: {{ $customStyles['fontFamily'] ?? 'Arial, sans-serif' }} !important;
            font-size: {{ $customStyles['fontSize'] ?? '14px' }} !important;
        }

        .card {
            border: {{ $borderStyle }} !important;
            border-radius: {{ $customStyles['customBorderRadius'] ?? $customStyles['borderRadius'] ?? '4px' }} !important;
        }

        /* Form Title Position */
        .form-title,
        h6.form-title,
        .h3.form-title,
        .card-body .form-title {
            text-align: {{ $customStyles['formTitlePosition'] ?? 'center' }} !important;
        }

        .form-group {
            margin-bottom: {{ $customStyles['fieldSpacing'] ?? '15px' }} !important;
        }

        .form-label {
            color: {{ $customStyles['labelColor'] ?? '#212529' }} !important;
            font-family: {{ $customStyles['fontFamily'] ?? 'Arial, sans-serif' }} !important;
        }

        .form-control, .form-control:focus, .form-select, .form-select:focus {
            background-color: {{ $customStyles['inputBackgroundColor'] ?? '#ffffff' }} !important;
            color: {{ $customStyles['inputColor'] ?? '#495057' }} !important;
            border-color: {{ $customStyles['borderColor'] ?? '#ced4da' }} !important;
            border-radius: {{ $customStyles['borderRadius'] ?? '4px' }} !important;
            font-family: {{ $customStyles['fontFamily'] ?? 'Arial, sans-serif' }} !important;
            font-size: {{ $customStyles['fontSize'] ?? '14px' }} !important;
        }

        .form-check-input {
            border-color: {{ $customStyles['borderColor'] ?? '#ced4da' }} !important;
        }

        .btn-primary {
            background-color: {{ $customStyles['buttonBackgroundColor'] ?? '#007bff' }} !important;
            color: {{ $customStyles['buttonColor'] ?? '#ffffff' }} !important;
            border-color: {{ $customStyles['buttonBackgroundColor'] ?? '#007bff' }} !important;
            border-radius: {{ $customStyles['borderRadius'] ?? '4px' }} !important;
            font-family: {{ $customStyles['fontFamily'] ?? 'Arial, sans-serif' }} !important;
            font-size: {{ $customStyles['fontSize'] ?? '14px' }} !important;
        }

        .btn-primary:hover, .btn-primary:focus, .btn-primary:active {
            @php
                $buttonColor = $customStyles['buttonBackgroundColor'] ?? '#007bff';
                // Convert hex to RGB and darken by 20%
                $hex = str_replace('#', '', $buttonColor);
                $r = hexdec(substr($hex, 0, 2));
                $g = hexdec(substr($hex, 2, 2));
                $b = hexdec(substr($hex, 4, 2));

                // Darken by 20%
                $r = max(0, $r - 51);
                $g = max(0, $g - 51);
                $b = max(0, $b - 51);

                $darkerColor = sprintf("#%02x%02x%02x", $r, $g, $b);
            @endphp
            background-color: {{ $darkerColor }} !important;
            border-color: {{ $darkerColor }} !important;
        }

        .form-check-label {
            color: {{ $customStyles['labelColor'] ?? '#212529' }} !important;
            font-family: {{ $customStyles['fontFamily'] ?? 'Arial, sans-serif' }} !important;
        }

        textarea.form-control {
            background-color: {{ $customStyles['inputBackgroundColor'] ?? '#ffffff' }} !important;
            color: {{ $customStyles['inputColor'] ?? '#495057' }} !important;
            border-color: {{ $customStyles['borderColor'] ?? '#ced4da' }} !important;
            border-radius: {{ $customStyles['borderRadius'] ?? '4px' }} !important;
        }

        /* Submit Button Position */
        @php
            $buttonPosition = $customStyles['buttonPosition'] ?? 'center';
            $buttonAlignment = 'center';
            if($buttonPosition === 'left') $buttonAlignment = 'flex-start';
            if($buttonPosition === 'right') $buttonAlignment = 'flex-end';
        @endphp

        .form-submit-container {
            display: flex;
            justify-content: {{ $buttonAlignment }};
            margin-top: 20px;
        }

        /* Hide Form Title */
        @php
            // Debug: Check the actual values
            echo "<!-- hide_title value: " . var_export($form->hide_title, true) . " -->";
            echo "<!-- hide_title type: " . gettype($form->hide_title) . " -->";
            echo "<!-- form id: " . $form->id . " -->";
            echo "<!-- customStyles isset: " . (isset($customStyles) ? 'true' : 'false') . " -->";
        @endphp
        @if($form->hide_title == 1 || $form->hide_title === true)
        .form-title,
        h6.form-title,
        .h3.form-title {
            display: none !important;
            visibility: hidden !important;
        }
        @endif
        @else
        /* Default Form Styles when no custom styles are set */
        .form-group {
            margin-bottom: 15px;
        }

        .card, .card-body {
            background-color: #ffffff;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        .card {
            border: 1px solid #ced4da;
            border-radius: 4px;
        }

        /* Form Title Position - Default Styles */
        .form-title,
        h6.form-title,
        .h3.form-title,
        .card-body .form-title {
            text-align: {{ isset($customStyles['formTitlePosition']) ? $customStyles['formTitlePosition'] : 'center' }} !important;
        }

        /* Hide Form Title - Default Styles */
        @if($form->hide_title == 1 || $form->hide_title === true || $form->hide_title === '1')
        .form-title,
        h6.form-title,
        .h3.form-title,
        .mb-4 .form-title,
        .card-body .form-title,
        .card-body h6.form-title,
        .card-body .h3.form-title {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        @endif
        @endif
    </style>

    <link rel="stylesheet" href="{{ asset('css/custom-color.css') }}">
</head>

<body class="{{$themeColor}}">

    <div class="dash-content">

        <div class="min-vh-100 py-5 d-flex align-items-center">
            <div class="w-100">
                <div class="row justify-content-center">
                    <div class="col-sm-8 col-lg-5">
                        {{-- Company logo hidden as requested --}}
                        {{-- <div class="row justify-content-center mb-3">
                            <a class="navbar-brand" href="#">
                                <img src="{{ $logo . '/' . $setting['company_logo_dark'] }}" class="navbar-brand-img big-logo">
                            </a>
                        </div> --}}
                        <div class="card shadow zindex-100 mb-0">
                            @if($form->is_active == 1)
                                {{Form::open(array('route'=>array('form.view.store'),'method'=>'post','enctype'=>'multipart/form-data','class'=>'mb-0'))}}
                                <div class="card-body px-md-5 py-5 overflow-hidden">
                                    <div class="mb-4" @if($form->hide_title == 1 || $form->hide_title === true || $form->hide_title === '1') style="display: none !important;" @endif>
                                        <h6 class="h3 form-title" @if($form->hide_title == 1 || $form->hide_title === true || $form->hide_title === '1') style="display: none !important;" @endif>{{$form->name}}</h6>
                                    </div>
                                    <input type="hidden" value="{{$code}}" name="code">
                                    @if($objFields && $objFields->count() > 0)
                                        @foreach($objFields as $objField)
                                            @php
                                                $isRequired = $objField->required == 1;
                                                $requiredAttr = $isRequired ? ['required' => 'required'] : [];
                                                $labelClass = 'form-label' . ($isRequired ? ' required' : '');
                                            @endphp
                                            @php
                                                // Generate default placeholder: "Enter [Label Name]"
                                                $defaultPlaceholder = 'Enter ' . $objField->name;
                                                $finalPlaceholder = $objField->placeholder ?: $defaultPlaceholder;
                                            @endphp
                                            @if($objField->type == 'text')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    {{ Form::text('field['.$objField->id.']', null, array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id, 'placeholder' => __($finalPlaceholder)], $requiredAttr)) }}
                                                </div>
                                            @elseif($objField->type == 'email')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    {{ Form::email('field['.$objField->id.']', null, array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id, 'placeholder' => __($finalPlaceholder)], $requiredAttr)) }}
                                                </div>
                                            @elseif($objField->type == 'number')
                                                @php
                                                    $isPhoneField = stripos($objField->name, 'contact') !== false ||
                                                                   stripos($objField->name, 'phone') !== false ||
                                                                   stripos($objField->name, 'mobile') !== false;

                                                    // Check if there's already a separate Country Code field in this form
                                                    $hasCountryCodeField = $objFields->contains(function($field) {
                                                        return stripos($field->name, 'country code') !== false ||
                                                               stripos($field->name, 'country_code') !== false;
                                                    });

                                                    // Don't show country dropdown if there's already a separate country code field
                                                    $showCountryDropdown = $isPhoneField && !$hasCountryCodeField;
                                                @endphp
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    @if($showCountryDropdown)
                                                        <div class="phone-input-container">
                                                        <div class="country-selector">
                                                            <button type="button" class="country-dropdown-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <span class="flag">🇮🇳</span>
                                                                <span class="code">+91</span>
                                                                <i class="ti ti-chevron-down"></i>
                                                            </button>
                                                            <ul class="dropdown-menu country-dropdown">
                                                                <li><a class="dropdown-item" href="#" data-country="IN" data-code="+91" data-flag="🇮🇳">🇮🇳 India (+91)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="US" data-code="+1" data-flag="🇺🇸">🇺🇸 United States (+1)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="GB" data-code="+44" data-flag="🇬🇧">🇬🇧 United Kingdom (+44)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="CN" data-code="+86" data-flag="🇨🇳">🇨🇳 China (+86)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="JP" data-code="+81" data-flag="🇯🇵">🇯🇵 Japan (+81)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="DE" data-code="+49" data-flag="🇩🇪">🇩🇪 Germany (+49)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="FR" data-code="+33" data-flag="🇫🇷">🇫🇷 France (+33)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="IT" data-code="+39" data-flag="🇮🇹">🇮🇹 Italy (+39)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="ES" data-code="+34" data-flag="🇪🇸">🇪🇸 Spain (+34)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="RU" data-code="+7" data-flag="🇷🇺">🇷🇺 Russia (+7)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="BR" data-code="+55" data-flag="🇧🇷">🇧🇷 Brazil (+55)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="AU" data-code="+61" data-flag="🇦🇺">🇦🇺 Australia (+61)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="KR" data-code="+82" data-flag="🇰🇷">🇰🇷 South Korea (+82)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="SG" data-code="+65" data-flag="🇸🇬">🇸🇬 Singapore (+65)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="MY" data-code="+60" data-flag="🇲🇾">🇲🇾 Malaysia (+60)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="TH" data-code="+66" data-flag="🇹🇭">🇹🇭 Thailand (+66)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="VN" data-code="+84" data-flag="🇻🇳">🇻🇳 Vietnam (+84)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="ID" data-code="+62" data-flag="🇮🇩">🇮🇩 Indonesia (+62)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="PH" data-code="+63" data-flag="🇵🇭">🇵🇭 Philippines (+63)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="AE" data-code="+971" data-flag="🇦🇪">🇦🇪 UAE (+971)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="SA" data-code="+966" data-flag="🇸🇦">🇸🇦 Saudi Arabia (+966)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="ZA" data-code="+27" data-flag="🇿🇦">🇿🇦 South Africa (+27)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="EG" data-code="+20" data-flag="🇪🇬">🇪🇬 Egypt (+20)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="MX" data-code="+52" data-flag="🇲🇽">🇲🇽 Mexico (+52)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="AR" data-code="+54" data-flag="🇦🇷">🇦🇷 Argentina (+54)</a></li>
                                                            </ul>
                                                        </div>
                                                        {{ Form::number('field['.$objField->id.']', null, array_merge(['class' => 'form-control phone-number-input', 'id' => 'field-'.$objField->id, 'placeholder' => __($finalPlaceholder)], $requiredAttr)) }}
                                                        <input type="hidden" name="country_code[{{ $objField->id }}]" value="+91" class="country-code-input">
                                                    </div>
                                                    @else
                                                        {{ Form::number('field['.$objField->id.']', null, array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id, 'placeholder' => __($finalPlaceholder)], $requiredAttr)) }}
                                                    @endif
                                                </div>
                                            @elseif($objField->type == 'date')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    {{ Form::date('field['.$objField->id.']', null, array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id, 'placeholder' => __($finalPlaceholder)], $requiredAttr)) }}
                                                </div>
                                            @elseif($objField->type == 'textarea')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    {{ Form::textarea('field['.$objField->id.']', null, array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id, 'placeholder' => __($finalPlaceholder)], $requiredAttr)) }}
                                                </div>
                                            @elseif($objField->type == 'select')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    @php
                                                        $options = is_array($objField->options) ? $objField->options : json_decode($objField->options, true);
                                                        $selectOptions = ['' => 'Select an option'];
                                                        if($options) {
                                                            foreach($options as $option) {
                                                                $selectOptions[$option] = $option;
                                                            }
                                                        }
                                                    @endphp
                                                    {{ Form::select('field['.$objField->id.']', $selectOptions, null, array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id], $requiredAttr)) }}
                                                </div>
                                            @elseif($objField->type == 'multiselect')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    @php
                                                        $options = is_array($objField->options) ? $objField->options : json_decode($objField->options, true);
                                                        $selectOptions = [];
                                                        if($options) {
                                                            foreach($options as $option) {
                                                                $selectOptions[$option] = $option;
                                                            }
                                                        }
                                                    @endphp
                                                    {{ Form::select('field['.$objField->id.'][]', $selectOptions, null, array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id, 'multiple' => 'multiple', 'size' => '4'], $requiredAttr)) }}
                                                    <small class="form-text text-muted">{{ __('Hold Ctrl (Cmd on Mac) to select multiple options') }}</small>
                                                </div>
                                            @elseif($objField->type == 'radio')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    @php $options = is_array($objField->options) ? $objField->options : json_decode($objField->options, true); @endphp
                                                    @if($options)
                                                        @foreach($options as $option)
                                                            <div class="form-check">
                                                                {{ Form::radio('field['.$objField->id.']', $option, false, array_merge(['class' => 'form-check-input', 'id' => 'field-'.$objField->id.'_'.$loop->index], $isRequired ? ['required' => 'required'] : [])) }}
                                                                {{ Form::label('field-'.$objField->id.'_'.$loop->index, $option, ['class' => 'form-check-label']) }}
                                                            </div>
                                                        @endforeach
                                                    @endif
                                                </div>
                                            @elseif($objField->type == 'checkbox')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    @php $options = is_array($objField->options) ? $objField->options : json_decode($objField->options, true); @endphp
                                                    @if($options)
                                                        @foreach($options as $option)
                                                            <div class="form-check">
                                                                {{ Form::checkbox('field['.$objField->id.'][]', $option, false, array_merge(['class' => 'form-check-input', 'id' => 'field-'.$objField->id.'_'.$loop->index], $isRequired && $loop->first ? ['required' => 'required'] : [])) }}
                                                                {{ Form::label('field-'.$objField->id.'_'.$loop->index, $option, ['class' => 'form-check-label']) }}
                                                            </div>
                                                        @endforeach
                                                    @endif
                                                </div>
                                            @elseif($objField->type == 'file')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    {{ Form::file('field['.$objField->id.']', array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id, 'accept' => '*/*'], $requiredAttr)) }}
                                                </div>
                                            @elseif($objField->type == 'file_multiple')
                                                <div class="form-group">
                                                    {{ Form::label('field-'.$objField->id, __($objField->name), ['class' => $labelClass]) }}
                                                    @if($isRequired)<span class="text-danger">*</span>@endif
                                                    {{ Form::file('field['.$objField->id.'][]', array_merge(['class' => 'form-control', 'id' => 'field-'.$objField->id, 'multiple' => 'multiple', 'accept' => '*/*'], $requiredAttr)) }}
                                                    <small class="form-text text-muted">{{ __('You can select multiple files') }}</small>
                                                </div>
                                            @endif
                                        @endforeach
                                        <div class="mt-4 form-submit-container">
                                            {{Form::submit(__('Enquiry Now'),array('class'=>'btn btn-primary'))}}
                                        </div>
                                        @endif
                                </div>

                                {{Form::close()}}
                            @else
                                <div class="page-title"><h5>{{__('Form is not active.')}}</h5></div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Footer and copyright section removed from public form --}}
    {{-- @include('partials.admin.footer') --}}

    @if($get_cookie['enable_cookie'] == 'on')
        @include('layouts.cookie_consent')
    @endif

    <script>
        // Country code dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle country dropdown selection
            document.querySelectorAll('.country-dropdown .dropdown-item').forEach(function(item) {
                item.addEventListener('click', function(e) {
                    e.preventDefault();

                    const country = this.getAttribute('data-country');
                    const code = this.getAttribute('data-code');
                    const flag = this.getAttribute('data-flag');

                    // Find the parent container
                    const container = this.closest('.phone-input-container');
                    const button = container.querySelector('.country-dropdown-btn');
                    const hiddenInput = container.querySelector('.country-code-input');

                    // Update button display
                    button.querySelector('.flag').textContent = flag;
                    button.querySelector('.code').textContent = code;

                    // Update hidden input value
                    if (hiddenInput) {
                        hiddenInput.value = code;
                    }

                    // Close dropdown
                    const dropdown = bootstrap.Dropdown.getInstance(button);
                    if (dropdown) {
                        dropdown.hide();
                    }
                });
            });
        });
    </script>

</body>

</html>
