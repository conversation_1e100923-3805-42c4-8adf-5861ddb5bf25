<?php

namespace App\Http\Controllers;

use App\Models\FormBuilder;
use App\Models\FormField;
use App\Models\FormFieldResponse;
use App\Models\FormResponse;
use App\Models\Lead;
use App\Models\LeadStage;
use App\Models\Pipeline;
use App\Models\User;
use App\Models\UserLead;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FormBuilderController extends Controller
{
    public function index()
    {
        $usr = \Auth::user();
        if($usr->can('manage form builder'))
        {
            $forms = FormBuilder::where('created_by', '=', $usr->creatorId())->get();

            return view('form_builder.index', compact('forms'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    public function create()
    {
        return view('form_builder.create');
    }

    public function store(Request $request)
    {
        if(\Auth::user()->can('create form builder'))
        {
            $validator = \Validator::make(
                $request->all(), [
                                   'name' => 'required',
                               ]
            );

            if($validator->fails())
            {
                $messages = $validator->getMessageBag();

                return redirect()->route('form_builder.index')->with('error', $messages->first());
            }

            $form_builder             = new FormBuilder();
            $form_builder->name       = $request->name;
            $form_builder->code       = uniqid() . time();
            $form_builder->is_active  = $request->is_active;
            $form_builder->created_by = \Auth::user()->creatorId();
            $form_builder->save();

            // Create default fields: Name, Email, and Contact No
            $this->createDefaultFields($form_builder->id, \Auth::user()->creatorId());

            return redirect()->route('form_builder.index')->with('success', __('Form successfully created.'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    public function show(FormBuilder $formBuilder)
    {
        if(\Auth::user()->can('manage form field'))
        {
            if($formBuilder->created_by == \Auth::user()->creatorId())
            {
                return view('form_builder.show', compact('formBuilder'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function edit(FormBuilder $formBuilder)
    {
        if(\Auth::user()->can('edit form builder'))
        {
            if($formBuilder->created_by == Auth::user()->creatorId())
            {
                return view('form_builder.edit', compact('formBuilder'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied.')], 401);
            }
        }
        else
        {
            return response()->json(['error' => __('Permission Denied.')], 401);
        }
    }


    public function update(Request $request, FormBuilder $formBuilder)
    {
        $usr = \Auth::user();
        if($usr->can('edit form builder'))
        {
            if($formBuilder->created_by == $usr->creatorId())
            {
                $validator = \Validator::make(
                    $request->all(), [
                                       'name' => 'required',
                                   ]
                );

                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->route('form_builder.index')->with('error', $messages->first());
                }

                $formBuilder->name           = $request->name;
                $formBuilder->is_active      = $request->is_active;
                $formBuilder->is_lead_active = 0;
                $formBuilder->save();

                return redirect()->route('form_builder.index')->with('success', __('Form successfully updated.'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    public function destroy(FormBuilder $formBuilder)
    {
        if(Auth::user()->can('delete form builder'))
        {
            if($formBuilder->created_by == \Auth::user()->ownerId())
            {
                FormField::where('form_id', '=', $formBuilder->id)->delete();
                FormFieldResponse::where('form_id', '=', $formBuilder->id)->delete();
                FormResponse::where('form_id', '=', $formBuilder->id)->delete();

                $formBuilder->delete();

                return redirect()->route('form_builder.index')->with('success', __('Form successfully deleted!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    // Field curd
    public function fieldCreate($id)
    {
        $usr = \Auth::user();
        if($usr->can('create form field'))
        {
            $formbuilder = FormBuilder::find($id);
            if($formbuilder->created_by == $usr->creatorId())
            {
                $types = FormBuilder::$fieldTypes;

                return view('form_builder.field_create', compact('types', 'formbuilder'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function fieldStore($id, Request $request)
    {
        $usr = \Auth::user();
        if($usr->can('create form field'))
        {
            $formbuilder = FormBuilder::find($id);
            if($formbuilder->created_by == $usr->creatorId())
            {
                $names = $request->name;
                $types = $request->type;
                $placeholders = $request->placeholder ?? [];
                $options = $request->options ?? [];
                $required = $request->required ?? [];
                $createdFields = [];

                foreach($names as $key => $value)
                {
                    if(!empty($value))
                    {
                        // Get options for this field if it's a type that supports options
                        $fieldOptions = null;
                        if (in_array($types[$key], ['checkbox', 'radio', 'select', 'multiselect']) && !empty($options)) {
                            $fieldOptions = array_values(array_filter($options)); // clean empty values
                        }

                        // Check if this field is required
                        $isRequired = isset($required[$key]) && $required[$key] == 1 ? 1 : 0;

                        // Get placeholder for this field
                        $fieldPlaceholder = isset($placeholders[$key]) ? $placeholders[$key] : '';

                        // create form field
                        $field = FormField::create(
                            [
                                'form_id' => $formbuilder->id,
                                'name' => $value,
                                'type' => $types[$key],
                                'placeholder' => $fieldPlaceholder,
                                'options' => $fieldOptions,
                                'required' => $isRequired,
                                'created_by' => $usr->creatorId(),
                            ]
                        );

                        // Also create a custom field for future reuse if it doesn't already exist
                        $this->createCustomFieldIfUnique($value, $types[$key], $fieldOptions, $usr->creatorId());

                        $createdFields[] = $field;
                    }
                }

                // Check if this is an AJAX request
                if ($request->ajax() || $request->wantsJson()) {
                    // Get all custom fields for the updated dropdown
                    $customFields = \App\Models\CustomField::where('created_by', $usr->creatorId())
                                                          ->where('module', 'Leads')
                                                          ->get();

                    return response()->json([
                        'success' => true,
                        'message' => __('Field successfully created.'),
                        'field' => $createdFields[0] ?? null, // Return the first created field
                        'customFields' => $customFields // Return updated custom fields list
                    ]);
                }

                return redirect()->back()->with('success', __('Field successfully created.'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function fieldEdit($id, $field_id)
    {
        $usr = \Auth::user();
        if($usr->can('edit form field'))
        {
            $form = FormBuilder::find($id);
            if($form && $form->created_by == $usr->creatorId())
            {
                $form_field = FormField::find($field_id);

                if(!empty($form_field))
                {
                    $types = FormBuilder::$fieldTypes;

                    return view('form_builder.field_edit', compact('form_field', 'types', 'form'));
                }
                else
                {
                    if(request()->ajax()) {
                        return response()->json(['error' => __('Field not found.')], 404);
                    }
                    return redirect()->back()->with('error', __('Field not found.'));
                }
            }
            else
            {
                if(request()->ajax()) {
                    return response()->json(['error' => __('Permission Denied.')], 403);
                }
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            if(request()->ajax()) {
                return response()->json(['error' => __('Permission denied.')], 403);
            }
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function fieldUpdate($id, $field_id, Request $request)
    {
        $usr = \Auth::user();
        if($usr->can('edit form field'))
        {
            $form = FormBuilder::find($id);
            if($form->created_by == $usr->creatorId())
            {
                $validator = \Validator::make(
                    $request->all(), [
                                       'name' => 'required',
                                   ]
                );
                if($validator->fails())
                {
                    $messages = $validator->getMessageBag();

                    return redirect()->back()->with('error', $messages->first());
                }

                $field = FormField::find($field_id);

                // Get options for this field if it's a type that supports options
                $fieldOptions = null;
                if (in_array($request->type, ['checkbox', 'radio', 'select', 'multiselect']) && $request->has('options')) {
                    $fieldOptions = array_values(array_filter($request->options)); // clean empty values
                }

                // Check if field is required
                $isRequired = $request->has('required') && $request->required == 1 ? 1 : 0;

                $field->update(
                    [
                        'name' => $request->name,
                        'type' => $request->type,
                        'placeholder' => $request->placeholder,
                        'options' => $fieldOptions,
                        'required' => $isRequired,
                    ]
                );

                return redirect()->back()->with('success', __('Form successfully updated.'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function customDesign()
    {
        $usr = \Auth::user();
        if($usr->can('manage form builder'))
        {
            $forms = FormBuilder::where('created_by', '=', $usr->creatorId())->get();
            return view('form_builder.custom_design', compact('forms'));
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function saveStyles(Request $request)
    {
        $usr = \Auth::user();
        if($usr->can('manage form builder'))
        {
            $formId = $request->form_id;
            $styles = $request->styles;

            // Validate and sanitize styles data
            $allowedStyles = [
                'backgroundColor',
                'backgroundTransparent',
                'inputBackgroundColor',
                'buttonBackgroundColor',
                'labelColor',
                'inputColor',
                'buttonColor',
                'buttonPosition',
                'fontFamily',
                'fontSize',
                'borderColor',
                'borderRadius',
                'customBorderRadius',
                'formTitlePosition',
                'fieldSpacing'
            ];

            $sanitizedStyles = [];
            foreach($allowedStyles as $style) {
                if(isset($styles[$style])) {
                    // Handle boolean values properly
                    if($style === 'backgroundTransparent') {
                        $sanitizedStyles[$style] = filter_var($styles[$style], FILTER_VALIDATE_BOOLEAN);
                    } else {
                        $sanitizedStyles[$style] = $styles[$style];
                    }
                }
            }

            // Find the form
            $form = FormBuilder::where('id', $formId)
                              ->where('created_by', $usr->creatorId())
                              ->first();

            if($form)
            {
                // Save styles as JSON in the form_styles column
                $form->form_styles = json_encode($sanitizedStyles);

                // Save hide_title as a separate boolean field
                // Handle both true/false values explicitly
                if (isset($styles['hideFormTitle'])) {
                    $form->hide_title = $styles['hideFormTitle'] === true || $styles['hideFormTitle'] === 'true' || $styles['hideFormTitle'] === 1;
                } else {
                    $form->hide_title = false;
                }

                $form->save();

                return response()->json([
                    'success' => true,
                    'message' => __('Styles saved successfully!')
                ]);
            }
            else
            {
                return response()->json([
                    'success' => false,
                    'message' => __('Form not found or permission denied.')
                ], 404);
            }
        }
        else
        {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }
    }

    public function getFormStyles($id)
    {
        $usr = \Auth::user();
        if($usr->can('manage form builder'))
        {
            $form = FormBuilder::where('id', $id)
                              ->where('created_by', $usr->creatorId())
                              ->first();

            if($form)
            {
                $styles = $form->form_styles ? json_decode($form->form_styles, true) : [];

                // Always add hide_title to styles
                $styles['hideFormTitle'] = (bool)$form->hide_title;

                $fields = $form->form_field;

                return response()->json([
                    'success' => true,
                    'styles' => $styles,
                    'fields' => $fields
                ]);
            }
            else
            {
                return response()->json([
                    'success' => false,
                    'message' => __('Form not found or permission denied.')
                ], 404);
            }
        }
        else
        {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }
    }

    public function customize($id)
    {
        $usr = \Auth::user();
        if($usr->can('manage form builder'))
        {
            $form = FormBuilder::where('id', $id)
                              ->where('created_by', $usr->creatorId())
                              ->first();

            if($form)
            {
                try {
                    // Get existing custom fields
                    $customFields = \App\Models\CustomField::where('created_by', $usr->creatorId())
                                                          ->where('module', 'Leads')
                                                          ->get();

                    // Load form fields to ensure relationship works
                    $form->load('form_field');

                    return view('form_builder.customize', compact('form', 'customFields'));
                } catch (\Exception $e) {
                    \Log::error('Form customization error: ' . $e->getMessage());
                    \Log::error('Stack trace: ' . $e->getTraceAsString());
                    return redirect()->back()->with('error', __('Error loading form customization: ') . $e->getMessage());
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Form not found or permission denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function addCustomField(Request $request, $id)
    {
        $usr = \Auth::user();
        if($usr->can('create form field'))
        {
            $form = FormBuilder::where('id', $id)
                              ->where('created_by', $usr->creatorId())
                              ->first();

            if($form)
            {
                \Log::info('AddCustomField: Form found', ['form_id' => $form->id, 'custom_field_id' => $request->custom_field_id]);

                $customField = \App\Models\CustomField::find($request->custom_field_id);

                if($customField && $customField->created_by == $usr->creatorId())
                {
                    \Log::info('AddCustomField: Custom field found', ['custom_field' => $customField->toArray()]);
                    // Get the next order number (add at bottom)
                    $maxOrder = FormField::where('form_id', $form->id)->max('order') ?? 0;
                    $nextOrder = $maxOrder + 1;

                    // Create a new form field based on the custom field
                    $formField = new \App\Models\FormField();
                    $formField->form_id = $form->id;
                    $formField->name = $customField->name;
                    $formField->type = $customField->type;
                    $formField->required = $request->required ?? 0;
                    $formField->order = $nextOrder;
                    $formField->created_by = $usr->creatorId();

                    // Handle options for select, radio, checkbox fields
                    if(in_array($customField->type, ['select', 'radio', 'checkbox', 'multiselect']) && $customField->options)
                    {
                        $formField->options = $customField->options; // Use array directly since CustomField model handles casting
                    }

                    $formField->save();

                    return response()->json([
                        'success' => true,
                        'message' => __('Custom field added successfully!')
                    ]);
                }
                else
                {
                    \Log::error('AddCustomField: Custom field not found or permission denied', [
                        'custom_field_id' => $request->custom_field_id,
                        'custom_field_exists' => $customField ? 'yes' : 'no',
                        'creator_id' => $usr->creatorId(),
                        'custom_field_creator' => $customField ? $customField->created_by : 'N/A'
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => __('Custom field not found.')
                    ], 404);
                }
            }
            else
            {
                return response()->json([
                    'success' => false,
                    'message' => __('Form not found or permission denied.')
                ], 404);
            }
        }
        else
        {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }
    }

    public function fieldDestroy($id, $field_id)
    {
        $usr = \Auth::user();
        if($usr->can('delete form field'))
        {
            $form = FormBuilder::find($id);
            if($form->created_by == $usr->creatorId())
            {
                $form_field_response = FormFieldResponse::orWhere('subject_id', '=', $field_id)->orWhere('name_id', '=', $field_id)->orWhere('email_id', '=', $field_id)->first();

                if(!empty($form_field_response))
                {
                    return redirect()->back()->with('error', __('Please remove this field from Convert Lead.'));
                }
                else
                {
                    $form_field = FormField::find($field_id);
                    if(!empty($form_field))
                    {
                        $form_field->delete();
                    }
                    else
                    {
                        return redirect()->back()->with('error', __('Field not found.'));
                    }


                    return redirect()->back()->with('success', __('Form successfully deleted.'));
                }
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    // For Response
    public function viewResponse($form_id)
    {
        if(Auth::user()->can('view form response'))
        {
            $form = FormBuilder::find($form_id);

            if(!empty($form))
            {
                if($form->created_by == \Auth::user()->creatorId())
                {
                    return view('form_builder.response', compact('form'));
                }
                else
                {
                    return response()->json(['error' => __('Permission Denied . ')], 401);
                }
            }

        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    // For Response Detail
    public function responseDetail($response_id)
    {
        if(Auth::user()->can('view form response'))
        {
            $formResponse = FormResponse::find($response_id);
            $form         = FormBuilder::find($formResponse->form_id);
            if($form->created_by == \Auth::user()->creatorId())
            {
                $response = json_decode($formResponse->response, true);

                return view('form_builder.response_detail', compact('response'));
            }
            else
            {
                return response()->json(['error' => __('Permission Denied . ')], 401);
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    // For Front Side View
    public function formView($code)
    {

        if(!empty($code))
        {
            // First check if this is a product payment hash
            $productPayment = \App\Models\ProductPayment::where('payment_hash', $code)
                ->where('is_active', true)
                ->first();

            if($productPayment) {
                // Redirect to product payment controller
                return app(\App\Http\Controllers\ProductPaymentController::class)->showPaymentForm($code);
            }

            // If not a product payment, check for form builder
            $form = FormBuilder::where('code', 'LIKE', $code)->first();

            if(!empty($form))
            {
                if($form->is_active == 1)
                {
                    $objFields = $form->form_field;

                    return view('form_builder.form_view', compact('objFields', 'code', 'form'));
                }
                else
                {
                    return view('form_builder.form_view', compact('code', 'form'));
                }
            }
            else
            {
                return redirect()->route('login')->with('error', __('Form not found please contact to admin.'));
            }
        }
        else
        {
            return redirect()->route('login')->with('error', __('Permission Denied.'));
        }
    }

    // For Front Side View Store
    public function formViewStore(Request $request)
    {
        // Debug log
        \Log::info('Form submission started', ['code' => $request->code]);

        // Get form
        $form = FormBuilder::where('code', 'LIKE', $request->code)->first();

        if(!empty($form))
        {
            $arrFieldResp = [];

            foreach($request->field as $key => $value)
            {
                $field = FormField::find($key);
                $fieldName = $field->name;

                // Handle file uploads
                if($field->type == 'file' || $field->type == 'file_multiple') {
                    if($request->hasFile("field.{$key}")) {
                        if($field->type == 'file_multiple') {
                            // Handle multiple files
                            $uploadedFiles = [];
                            foreach($request->file("field.{$key}") as $file) {
                                if($file->isValid()) {
                                    $fileName = time() . '_' . $file->getClientOriginalName();
                                    $file->storeAs('public/form_uploads', $fileName);
                                    $uploadedFiles[] = $fileName;
                                }
                            }
                            $arrFieldResp[$fieldName] = !empty($uploadedFiles) ? implode(', ', $uploadedFiles) : '-';
                        } else {
                            // Handle single file
                            $file = $request->file("field.{$key}");
                            if($file->isValid()) {
                                $fileName = time() . '_' . $file->getClientOriginalName();
                                $file->storeAs('public/form_uploads', $fileName);
                                $arrFieldResp[$fieldName] = $fileName;
                            } else {
                                $arrFieldResp[$fieldName] = '-';
                            }
                        }
                    } else {
                        $arrFieldResp[$fieldName] = '-';
                    }
                } else {
                    // Handle regular fields
                    $arrFieldResp[$fieldName] = (!empty($value)) ? $value : '-';
                }
            }

            // store response
            FormResponse::create(
                [
                    'form_id' => $form->id,
                    'response' => json_encode($arrFieldResp),
                ]
            );

            // in form convert lead is active then creat lead
            if($form->is_lead_active == 1)
            {
                $objField = $form->fieldResponse;

                // validation - but still allow form submission to complete
                $email = User::where('email', 'LIKE', $request->field[$objField->email_id])->first();

                if(!empty($email))
                {
                    // Log the issue but don't stop the form submission
                    \Log::info('Form submission: Email already exists in records', ['email' => $request->field[$objField->email_id]]);
                    // Continue with form submission instead of returning error
                }

                $usr   = User::find($form->created_by);
                $stage = LeadStage::where('pipeline_id', '=', $objField->pipeline_id)->where('created_by',$form->created_by)->first();

                if(!empty($stage))
                {
                    $lead              = new Lead();
                    $lead->name        = $request->field[$objField->name_id];
                    $lead->email       = $request->field[$objField->email_id];
                    $lead->subject     = $request->field[$objField->subject_id];
                    $lead->phone       = $request->field[$objField->phone_id];
                    $lead->user_id     = $objField->user_id;
                    $lead->pipeline_id = $objField->pipeline_id;
                    $lead->stage_id    = $stage->id;
                    $lead->created_by  = $usr->creatorId();
                    $lead->date        = date('Y-m-d');
                    $lead->save();

                    $usrLeads = [
                        $usr->id,
                        $objField->user_id,
                    ];

                    foreach($usrLeads as $usrLead)
                    {
                        UserLead::create(
                            [
                                'user_id' => $usrLead,
                                'lead_id' => $lead->id,
                            ]
                        );
                    }
                }
            }

            \Log::info('Form submission successful, redirecting to thank you page');
            return redirect()->route('form.thank_you')->with('success', __('Data submit successfully.'));
        }
        else
        {
            return redirect()->route('login')->with('error', __('Something went wrong.'));
        }

    }

    // Thank You Page
    public function thankYou()
    {
        return view('form_builder.thank_you');
    }

    // Convert into lead Modal
        public function formFieldBind($form_id)
    {
        $usr = \Auth::user();
        if($usr->type == 'company')
        {
            $form = FormBuilder::find($form_id);

            if($form->created_by == $usr->creatorId())
            {
                $types = $form->form_field->pluck('name', 'id');

                $formField = FormFieldResponse::where('form_id', '=', $form_id)->first();

                // Get Users
                $users = User::where('created_by', '=', $usr->creatorId())->where('type', '!=', 'client')->get()->pluck('name', 'id');

                // Pipelines
                $pipelines = Pipeline::where('created_by', '=', $usr->creatorId())->get()->pluck('name', 'id');

                return view('form_builder.form_field', compact('form', 'types', 'formField', 'users', 'pipelines'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    // Store convert into lead modal
    public function bindStore(Request $request, $id)
    {

        $usr = Auth::user();
        if($usr->type == 'company')
        {
            $form                 = FormBuilder::find($id);
            $form->is_lead_active = $request->is_lead_active;
            $form->save();

            if($form->created_by == $usr->creatorId())
            {
                if($form->is_lead_active == 1)
                {
                    $validator = \Validator::make(
                        $request->all(), [
                                           'subject_id' => 'required',
                                           'name_id' => 'required',
                                           'email_id' => 'required',
                                           'phone_id' => 'required',
                                           'user_id' => 'required',
                                           'pipeline_id' => 'required',
                                       ]
                    );

                    if($validator->fails())
                    {
                        $messages = $validator->getMessageBag();

                        // if validation failed then make status 0
                        $form->is_lead_active = 0;
                        $form->save();

                        return redirect()->back()->with('error', $messages->first());
                    }

                    if(!empty($request->form_response_id))
                    {
                        // if record already exists then update it.
                        $field_bind = FormFieldResponse::find($request->form_response_id);
                        $field_bind->form_id = $request->form_id;
                        $field_bind->subject_id = $request->subject_id;
                        $field_bind->name_id = $request->name_id;
                        $field_bind->email_id = $request->email_id;
                        $field_bind->phone_id = $request->phone_id;
                        $field_bind->user_id = $request->user_id;
                        $field_bind->pipeline_id = $request->pipeline_id;
                        $field_bind->save();
                    }
                    else
                    {
                        // Create Field Binding record on form_field_responses tbl
                        $field_bind = new FormFieldResponse();
                        $field_bind->form_id = $request->form_id;
                        $field_bind->subject_id = $request->subject_id;
                        $field_bind->name_id = $request->name_id;
                        $field_bind->email_id = $request->email_id;
                        $field_bind->phone_id = $request->phone_id;
                        $field_bind->user_id = $request->user_id;
                        $field_bind->pipeline_id = $request->pipeline_id;
                        $field_bind->save();
                    }
                }

                return redirect()->back()->with('success', __('Setting saved successfully!'));
            }
            else
            {
                return redirect()->back()->with('error', __('Permission Denied.'));
            }
        }
        else
        {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function createField(Request $request, $id)
    {
        $usr = \Auth::user();
        if($usr->can('create form field'))
        {
            $form = FormBuilder::where('id', $id)
                              ->where('created_by', $usr->creatorId())
                              ->first();

            if($form)
            {
                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required',
                        'type' => 'required',
                    ]
                );

                if($validator->fails())
                {
                    return response()->json([
                        'success' => false,
                        'message' => $validator->getMessageBag()->first()
                    ], 422);
                }

                // Get options for this field if it's a type that supports options
                $fieldOptions = null;
                if (in_array($request->type, ['checkbox', 'radio', 'select', 'multiselect']) && $request->options) {
                    $fieldOptions = is_array($request->options) ? $request->options : explode(',', $request->options);
                    $fieldOptions = array_values(array_filter($fieldOptions)); // clean empty values
                }

                // Check if this field is required
                $isRequired = $request->required == 1 ? 1 : 0;

                // Get the next order number (add at bottom)
                $maxOrder = FormField::where('form_id', $form->id)->max('order') ?? 0;
                $nextOrder = $maxOrder + 1;

                // create form field
                FormField::create([
                    'form_id' => $form->id,
                    'name' => $request->name,
                    'type' => $request->type,
                    'options' => $fieldOptions,
                    'required' => $isRequired,
                    'placeholder' => $request->placeholder,
                    'order' => $nextOrder,
                    'created_by' => $usr->creatorId(),
                ]);

                return response()->json([
                    'success' => true,
                    'message' => __('Field created successfully!')
                ]);
            }
            else
            {
                return response()->json([
                    'success' => false,
                    'message' => __('Form not found or permission denied.')
                ], 404);
            }
        }
        else
        {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }
    }

    public function getField($fieldId)
    {
        $usr = \Auth::user();
        if($usr->can('edit form field'))
        {
            $field = \App\Models\FormField::whereHas('form', function($query) use ($usr) {
                $query->where('created_by', $usr->creatorId());
            })->find($fieldId);

            if($field)
            {
                // Ensure options is properly formatted
                $fieldData = $field->toArray();
                if($field->options && is_string($field->options)) {
                    $fieldData['options'] = json_decode($field->options, true);
                }

                return response()->json($fieldData);
            }
            else
            {
                return response()->json([
                    'success' => false,
                    'message' => __('Field not found or permission denied.')
                ], 404);
            }
        }
        else
        {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }
    }

    public function updateField(Request $request, $fieldId)
    {
        $usr = \Auth::user();
        if($usr->can('edit form field'))
        {
            $field = \App\Models\FormField::whereHas('form', function($query) use ($usr) {
                $query->where('created_by', $usr->creatorId());
            })->find($fieldId);

            if($field)
            {
                $validator = \Validator::make(
                    $request->all(), [
                        'name' => 'required',
                        'type' => 'required',
                    ]
                );

                if($validator->fails())
                {
                    return response()->json([
                        'success' => false,
                        'message' => $validator->getMessageBag()->first()
                    ], 422);
                }

                // Get options for this field if it's a type that supports options
                $fieldOptions = null;
                if (in_array($request->type, ['checkbox', 'radio', 'select', 'multiselect']) && $request->options) {
                    $fieldOptions = is_array($request->options) ? $request->options : explode(',', $request->options);
                    $fieldOptions = array_values(array_filter($fieldOptions)); // clean empty values
                }

                // Check if this field is required
                $isRequired = $request->required == 1 ? 1 : 0;

                $field->name = $request->name;
                $field->type = $request->type;
                $field->placeholder = $request->placeholder;
                $field->required = $isRequired;
                $field->options = $fieldOptions;

                $field->save();

                return response()->json([
                    'success' => true,
                    'message' => __('Field updated successfully!')
                ]);
            }
            else
            {
                return response()->json([
                    'success' => false,
                    'message' => __('Field not found or permission denied.')
                ], 404);
            }
        }
        else
        {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }
    }

    public function deleteField($fieldId)
    {
        $usr = \Auth::user();
        if($usr->can('delete form field'))
        {
            $field = \App\Models\FormField::whereHas('form', function($query) use ($usr) {
                $query->where('created_by', $usr->creatorId());
            })->find($fieldId);

            if($field)
            {
                // Check if field is used in form responses (similar to existing logic)
                $form_field_response = \App\Models\FormFieldResponse::orWhere('subject_id', '=', $fieldId)
                                                                   ->orWhere('name_id', '=', $fieldId)
                                                                   ->orWhere('email_id', '=', $fieldId)
                                                                   ->first();

                if(!empty($form_field_response))
                {
                    return response()->json([
                        'success' => false,
                        'message' => __('Please remove this field from Convert Lead.')
                    ], 422);
                }
                else
                {
                    $field->delete();

                    return response()->json([
                        'success' => true,
                        'message' => __('Field deleted successfully!')
                    ]);
                }
            }
            else
            {
                return response()->json([
                    'success' => false,
                    'message' => __('Field not found or permission denied.')
                ], 404);
            }
        }
        else
        {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }
    }

    public function reorderFields(Request $request, $id)
    {
        $usr = \Auth::user();
        if($usr->can('manage form builder'))
        {
            $form = FormBuilder::where('id', $id)
                              ->where('created_by', $usr->creatorId())
                              ->first();

            if($form)
            {
                foreach($request->field_order as $fieldOrder)
                {
                    \App\Models\FormField::where('id', $fieldOrder['id'])
                                        ->where('form_id', $form->id)
                                        ->update(['order' => $fieldOrder['order']]);
                }

                return response()->json([
                    'success' => true,
                    'message' => __('Field order updated successfully!')
                ]);
            }
            else
            {
                return response()->json([
                    'success' => false,
                    'message' => __('Form not found or permission denied.')
                ], 404);
            }
        }
        else
        {
            return response()->json([
                'success' => false,
                'message' => __('Permission denied.')
            ], 403);
        }
    }

    /**
     * Create a custom field if it doesn't already exist with the same name
     * This allows newly created fields to be reused in other forms
     */
    private function createCustomFieldIfUnique($fieldName, $fieldType, $fieldOptions, $createdBy)
    {
        try {
            // Check if a custom field with this name already exists for this user
            $existingCustomField = \App\Models\CustomField::where('name', $fieldName)
                                                         ->where('created_by', $createdBy)
                                                         ->where('module', 'Leads')
                                                         ->first();

            if (!$existingCustomField) {
                // Generate unique key for the custom field
                $uniqueKey = \Str::slug($fieldName . '-' . time());

                // Ensure unique key is actually unique
                while (\App\Models\CustomField::where('unique_key', $uniqueKey)->exists()) {
                    $uniqueKey = \Str::slug($fieldName . '-' . time() . '-' . rand(1000, 9999));
                }

                // Create the custom field
                $customField = \App\Models\CustomField::create([
                    'name' => $fieldName,
                    'type' => $fieldType,
                    'module' => 'Leads', // Using 'Leads' module as seen in the customize method
                    'created_by' => $createdBy,
                    'unique_key' => $uniqueKey,
                    'options' => $fieldOptions
                ]);

                \Log::info('Created new custom field for reuse', [
                    'field_name' => $fieldName,
                    'field_type' => $fieldType,
                    'custom_field_id' => $customField->id,
                    'unique_key' => $uniqueKey
                ]);

                return $customField;
            } else {
                \Log::info('Custom field already exists, skipping creation', [
                    'field_name' => $fieldName,
                    'existing_custom_field_id' => $existingCustomField->id
                ]);

                return $existingCustomField;
            }
        } catch (\Exception $e) {
            \Log::error('Error creating custom field', [
                'field_name' => $fieldName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Don't fail the main field creation if custom field creation fails
            return null;
        }
    }

    /**
     * Create default fields for a new form
     */
    private function createDefaultFields($formId, $createdBy)
    {
        $defaultFields = [
            [
                'name' => 'Name',
                'type' => 'text',
                'placeholder' => 'Enter your full name',
                'required' => 1,
                'order' => 1,
                'options' => null
            ],
            [
                'name' => 'Email',
                'type' => 'email',
                'placeholder' => 'Enter your email address',
                'required' => 1,
                'order' => 2,
                'options' => null
            ],
            [
                'name' => 'Contact',
                'type' => 'tel',
                'placeholder' => 'Enter your phone number',
                'required' => 1,
                'order' => 3,
                'options' => null
            ]
        ];

        foreach ($defaultFields as $fieldData) {
            FormField::create([
                'form_id' => $formId,
                'name' => $fieldData['name'],
                'type' => $fieldData['type'],
                'placeholder' => $fieldData['placeholder'],
                'required' => $fieldData['required'],
                'order' => $fieldData['order'],
                'options' => $fieldData['options'],
                'created_by' => $createdBy,
            ]);

            // Also create custom fields for future reuse
            $this->createCustomFieldIfUnique($fieldData['name'], $fieldData['type'], $fieldData['options'], $createdBy);
        }
    }
}
