{"__meta": {"id": "01K1WN35APXCP64351BDPSFTGG", "datetime": "2025-08-05 08:20:46", "utime": 1754382046.550343, "method": "GET", "uri": "/form_builder/7/customize", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "Undefined variable $formbuilder (View: C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views\\form_builder\\customize.blade.php)", "code": 0, "file": "storage/framework/views/9862194a0baa09a44222d30f50d0ca4b.php", "line": 4141, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-479745376 data-indent-pad=\"  \"><span class=sf-dump-note>array:75</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">[object ErrorException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views/9862194a0baa09a44222d30f50d0ca4b.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref2330 title=\"2 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref27 title=\"2 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref24337 title=\"2 occurrences\">#4337</a><samp data-depth=5 id=sf-dump-479745376-ref24337 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>form</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormBuilder\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormBuilder</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref25733 title=\"2 occurrences\">#5733</a><samp data-depth=5 id=sf-dump-479745376-ref25733 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">form_builders</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Test Form</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"23 characters\">6891becc855a31754382028</span>\"\n            \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_lead_active</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>form_styles</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>hide_title</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Test Form</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"23 characters\">6891becc855a31754382028</span>\"\n            \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_lead_active</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>form_styles</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>hide_title</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>form_field</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#7609</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormField</span></span> {<a class=sf-dump-ref>#5737</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">form_fields</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Enter your full name</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Enter your full name</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">placeholder</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormField</span></span> {<a class=sf-dump-ref>#6563</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">form_fields</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>30</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Enter your email address</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>30</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Enter your email address</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">placeholder</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormField</span></span> {<a class=sf-dump-ref>#7048</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">form_fields</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>31</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Country Code</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Select country code</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2813 characters\">[&quot;\\ud83c\\uddee\\ud83c\\uddf3 India (+91)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddf8 United States (+1)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udde7 United Kingdom (+44)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udde6 Canada (+1)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddfa Australia (+61)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddea Germany (+49)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddf7 France (+33)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf9 Italy (+39)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddf8 Spain (+34)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf1 Netherlands (+31)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddea Belgium (+32)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udded Switzerland (+41)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf9 Austria (+43)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddea Sweden (+46)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf4 Norway (+47)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddf0 Denmark (+45)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddee Finland (+358)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf1 Poland (+48)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddff Czech Republic (+420)&quot;,&quot;\\ud83c\\udded\\ud83c\\uddfa Hungary (+36)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddf4 Romania (+40)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddec Bulgaria (+359)&quot;,&quot;\\ud83c\\uddec\\ud83c\\uddf7 Greece (+30)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf9 Portugal (+351)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddea Ireland (+353)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddfa Russia (+7)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\udde6 Ukraine (+380)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\uddf7 Turkey (+90)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf1 Israel (+972)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddea United Arab Emirates (+971)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\udde6 Saudi Arabia (+966)&quot;,&quot;\\ud83c\\uddf6\\ud83c\\udde6 Qatar (+974)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddfc Kuwait (+965)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udded Bahrain (+973)&quot;,&quot;\\ud83c\\uddf4\\ud83c\\uddf2 Oman (+968)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf4 Jordan (+962)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\udde7 Lebanon (+961)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddec Egypt (+20)&quot;,&quot;\\ud83c\\uddff\\ud83c\\udde6 South Africa (+27)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddec Nigeria (+234)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddea Kenya (+254)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udded Ghana (+233)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf3 China (+86)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf5 Japan (+81)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddf7 South Korea (+82)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddec Singapore (+65)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfe Malaysia (+60)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\udded Thailand (+66)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddf3 Vietnam (+84)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\udded Philippines (+63)&quot;,&quot;\\ud83c\\uddee\\ud83c\\udde9 Indonesia (+62)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udde9 Bangladesh (+880)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf0 Pakistan (+92)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\uddf0 Sri Lanka (+94)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf5 Nepal (+977)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf7 Brazil (+55)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfd Mexico (+52)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf7 Argentina (+54)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf1 Chile (+56)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf4 Colombia (+57)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddea Peru (+51)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddea Venezuela (+58)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddfe Uruguay (+598)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddfe Paraguay (+595)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf4 Bolivia (+591)&quot;,&quot;\\ud83c\\uddea\\ud83c\\udde8 Ecuador (+593)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddff New Zealand (+64)&quot;]</span>\"\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>31</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Country Code</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Select country code</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2813 characters\">[&quot;\\ud83c\\uddee\\ud83c\\uddf3 India (+91)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddf8 United States (+1)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udde7 United Kingdom (+44)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udde6 Canada (+1)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddfa Australia (+61)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddea Germany (+49)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddf7 France (+33)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf9 Italy (+39)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddf8 Spain (+34)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf1 Netherlands (+31)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddea Belgium (+32)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udded Switzerland (+41)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf9 Austria (+43)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddea Sweden (+46)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf4 Norway (+47)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddf0 Denmark (+45)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddee Finland (+358)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf1 Poland (+48)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddff Czech Republic (+420)&quot;,&quot;\\ud83c\\udded\\ud83c\\uddfa Hungary (+36)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddf4 Romania (+40)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddec Bulgaria (+359)&quot;,&quot;\\ud83c\\uddec\\ud83c\\uddf7 Greece (+30)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf9 Portugal (+351)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddea Ireland (+353)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddfa Russia (+7)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\udde6 Ukraine (+380)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\uddf7 Turkey (+90)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf1 Israel (+972)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddea United Arab Emirates (+971)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\udde6 Saudi Arabia (+966)&quot;,&quot;\\ud83c\\uddf6\\ud83c\\udde6 Qatar (+974)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddfc Kuwait (+965)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udded Bahrain (+973)&quot;,&quot;\\ud83c\\uddf4\\ud83c\\uddf2 Oman (+968)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf4 Jordan (+962)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\udde7 Lebanon (+961)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddec Egypt (+20)&quot;,&quot;\\ud83c\\uddff\\ud83c\\udde6 South Africa (+27)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddec Nigeria (+234)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddea Kenya (+254)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udded Ghana (+233)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf3 China (+86)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf5 Japan (+81)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddf7 South Korea (+82)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddec Singapore (+65)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfe Malaysia (+60)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\udded Thailand (+66)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddf3 Vietnam (+84)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\udded Philippines (+63)&quot;,&quot;\\ud83c\\uddee\\ud83c\\udde9 Indonesia (+62)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udde9 Bangladesh (+880)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf0 Pakistan (+92)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\uddf0 Sri Lanka (+94)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf5 Nepal (+977)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf7 Brazil (+55)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfd Mexico (+52)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf7 Argentina (+54)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf1 Chile (+56)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf4 Colombia (+57)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddea Peru (+51)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddea Venezuela (+58)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddfe Uruguay (+598)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddfe Paraguay (+595)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf4 Bolivia (+591)&quot;,&quot;\\ud83c\\uddea\\ud83c\\udde8 Ecuador (+593)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddff New Zealand (+64)&quot;]</span>\"\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">placeholder</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormField</span></span> {<a class=sf-dump-ref>#8842</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">form_fields</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>32</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Enter your phone number</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>32</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Enter your phone number</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">placeholder</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">form_styles</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">hide_title</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>customFields</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref28847 title=\"2 occurrences\">#8847</a><samp data-depth=5 id=sf-dump-479745376-ref28847 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#5735</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Gender</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"16 characters\">{{leads.gender}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">radio</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:16:10</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 05:37:47</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[&quot;Male&quot;,&quot;Female&quot;,&quot;Custom&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Gender</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"16 characters\">{{leads.gender}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">radio</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:16:10</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 05:37:47</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[&quot;Male&quot;,&quot;Female&quot;,&quot;Custom&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#6565</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"17 characters\">{{leads.country}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:53:49</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:53:49</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[&quot;India&quot;,&quot;Bangladesh&quot;,&quot;Nepal&quot;,&quot;Bhutan&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"17 characters\">{{leads.country}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:53:49</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:53:49</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[&quot;India&quot;,&quot;Bangladesh&quot;,&quot;Nepal&quot;,&quot;Bhutan&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#7046</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Subject</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"17 characters\">{{leads.subject}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:54:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:54:35</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[&quot;Node Js&quot;,&quot;Python&quot;,&quot;C&quot;,&quot;C++&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Subject</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"17 characters\">{{leads.subject}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:54:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:54:35</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[&quot;Node Js&quot;,&quot;Python&quot;,&quot;C&quot;,&quot;C++&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#8844</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Photo</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"15 characters\">{{leads.photo}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:55:26</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:55:26</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Photo</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"15 characters\">{{leads.photo}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:55:26</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:55:26</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#7935</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Link</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"14 characters\">{{leads.link}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">link</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:56:09</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:56:09</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Link</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"14 characters\">{{leads.link}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">link</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:56:09</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:56:09</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#6564</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"15 characters\">name-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"15 characters\">name-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#5736</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"16 characters\">email-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"16 characters\">email-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#9732</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>14</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Contact No</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"21 characters\">contact-no-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>14</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Contact No</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"21 characters\">contact-no-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#7047</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Country Code</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">country-code-1754382028</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2813 characters\">[&quot;\\ud83c\\uddee\\ud83c\\uddf3 India (+91)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddf8 United States (+1)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udde7 United Kingdom (+44)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udde6 Canada (+1)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddfa Australia (+61)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddea Germany (+49)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddf7 France (+33)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf9 Italy (+39)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddf8 Spain (+34)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf1 Netherlands (+31)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddea Belgium (+32)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udded Switzerland (+41)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf9 Austria (+43)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddea Sweden (+46)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf4 Norway (+47)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddf0 Denmark (+45)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddee Finland (+358)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf1 Poland (+48)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddff Czech Republic (+420)&quot;,&quot;\\ud83c\\udded\\ud83c\\uddfa Hungary (+36)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddf4 Romania (+40)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddec Bulgaria (+359)&quot;,&quot;\\ud83c\\uddec\\ud83c\\uddf7 Greece (+30)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf9 Portugal (+351)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddea Ireland (+353)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddfa Russia (+7)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\udde6 Ukraine (+380)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\uddf7 Turkey (+90)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf1 Israel (+972)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddea United Arab Emirates (+971)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\udde6 Saudi Arabia (+966)&quot;,&quot;\\ud83c\\uddf6\\ud83c\\udde6 Qatar (+974)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddfc Kuwait (+965)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udded Bahrain (+973)&quot;,&quot;\\ud83c\\uddf4\\ud83c\\uddf2 Oman (+968)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf4 Jordan (+962)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\udde7 Lebanon (+961)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddec Egypt (+20)&quot;,&quot;\\ud83c\\uddff\\ud83c\\udde6 South Africa (+27)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddec Nigeria (+234)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddea Kenya (+254)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udded Ghana (+233)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf3 China (+86)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf5 Japan (+81)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddf7 South Korea (+82)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddec Singapore (+65)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfe Malaysia (+60)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\udded Thailand (+66)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddf3 Vietnam (+84)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\udded Philippines (+63)&quot;,&quot;\\ud83c\\uddee\\ud83c\\udde9 Indonesia (+62)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udde9 Bangladesh (+880)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf0 Pakistan (+92)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\uddf0 Sri Lanka (+94)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf5 Nepal (+977)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf7 Brazil (+55)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfd Mexico (+52)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf7 Argentina (+54)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf1 Chile (+56)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf4 Colombia (+57)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddea Peru (+51)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddea Venezuela (+58)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddfe Uruguay (+598)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddfe Paraguay (+595)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf4 Bolivia (+591)&quot;,&quot;\\ud83c\\uddea\\ud83c\\udde8 Ecuador (+593)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddff New Zealand (+64)&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Country Code</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">country-code-1754382028</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2813 characters\">[&quot;\\ud83c\\uddee\\ud83c\\uddf3 India (+91)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddf8 United States (+1)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udde7 United Kingdom (+44)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udde6 Canada (+1)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddfa Australia (+61)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddea Germany (+49)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddf7 France (+33)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf9 Italy (+39)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddf8 Spain (+34)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf1 Netherlands (+31)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddea Belgium (+32)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udded Switzerland (+41)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf9 Austria (+43)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddea Sweden (+46)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf4 Norway (+47)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddf0 Denmark (+45)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddee Finland (+358)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf1 Poland (+48)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddff Czech Republic (+420)&quot;,&quot;\\ud83c\\udded\\ud83c\\uddfa Hungary (+36)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddf4 Romania (+40)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddec Bulgaria (+359)&quot;,&quot;\\ud83c\\uddec\\ud83c\\uddf7 Greece (+30)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf9 Portugal (+351)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddea Ireland (+353)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddfa Russia (+7)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\udde6 Ukraine (+380)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\uddf7 Turkey (+90)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf1 Israel (+972)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddea United Arab Emirates (+971)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\udde6 Saudi Arabia (+966)&quot;,&quot;\\ud83c\\uddf6\\ud83c\\udde6 Qatar (+974)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddfc Kuwait (+965)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udded Bahrain (+973)&quot;,&quot;\\ud83c\\uddf4\\ud83c\\uddf2 Oman (+968)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf4 Jordan (+962)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\udde7 Lebanon (+961)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddec Egypt (+20)&quot;,&quot;\\ud83c\\uddff\\ud83c\\udde6 South Africa (+27)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddec Nigeria (+234)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddea Kenya (+254)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udded Ghana (+233)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf3 China (+86)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf5 Japan (+81)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddf7 South Korea (+82)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddec Singapore (+65)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfe Malaysia (+60)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\udded Thailand (+66)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddf3 Vietnam (+84)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\udded Philippines (+63)&quot;,&quot;\\ud83c\\uddee\\ud83c\\udde9 Indonesia (+62)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udde9 Bangladesh (+880)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf0 Pakistan (+92)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\uddf0 Sri Lanka (+94)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf5 Nepal (+977)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf7 Brazil (+55)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfd Mexico (+52)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf7 Argentina (+54)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf1 Chile (+56)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf4 Colombia (+57)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddea Peru (+51)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddea Venezuela (+58)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddfe Uruguay (+598)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddfe Paraguay (+595)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf4 Bolivia (+591)&quot;,&quot;\\ud83c\\uddea\\ud83c\\udde8 Ecuador (+593)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddff New Zealand (+64)&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#9170</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">phone-number-1754382028</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">phone-number-1754382028</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"104 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/form_builder/customize.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref2330 title=\"2 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref27 title=\"2 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref24337 title=\"2 occurrences\">#4337</a>}\n        \"<span class=sf-dump-key>form</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormBuilder\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormBuilder</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref25733 title=\"2 occurrences\">#5733</a>}\n        \"<span class=sf-dump-key>customFields</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-479745376-ref28847 title=\"2 occurrences\">#8847</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>192</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"27 characters\">app/Http/Middleware/XSS.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>65</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Http\\Middleware\\XSS</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>41</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">app/Http/Middleware/EnsurePermissionsAfterPost.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Middleware\\EnsurePermissionsAfterPost</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"37 characters\">app/Http/Middleware/FilterRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Http\\Middleware\\FilterRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-479745376\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                                                                   stripos($field->name, 'mobile') !== false;\r\n", "\r\n", "                                                    // Check if there's already a separate Country Code field in this form\r\n", "                                                    $hasCountryCodeField = $formbuilder->form_field->contains(function($formField) {\r\n", "                                                        return stripos($formField->name, 'country code') !== false ||\r\n", "                                                               stripos($formField->name, 'country_code') !== false;\r\n", "                                                    });\r\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fstorage%2Fframework%2Fviews%2F9862194a0baa09a44222d30f50d0ca4b.php&line=4141", "ajax": false, "filename": "9862194a0baa09a44222d30f50d0ca4b.php", "line": "4141"}}, {"type": "ErrorException", "message": "Undefined variable $formbuilder", "code": 0, "file": "storage/framework/views/9862194a0baa09a44222d30f50d0ca4b.php", "line": 4141, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-712697977 data-indent-pad=\"  \"><span class=sf-dump-note>array:79</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>256</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">handleError</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"31 characters\">Undefined variable $formbuilder</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views\\9862194a0baa09a44222d30f50d0ca4b.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>4141</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">storage/framework/views/9862194a0baa09a44222d30f50d0ca4b.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>4141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Illuminate\\Foundation\\Bootstrap\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Foundation\\Bootstrap\\HandleExceptions</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>2</span>\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"31 characters\">Undefined variable $formbuilder</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views\\9862194a0baa09a44222d30f50d0ca4b.php</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-num>4141</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>123</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views\\9862194a0baa09a44222d30f50d0ca4b.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views/9862194a0baa09a44222d30f50d0ca4b.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref2330 title=\"3 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref27 title=\"3 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref24337 title=\"3 occurrences\">#4337</a><samp data-depth=5 id=sf-dump-712697977-ref24337 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>form</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormBuilder\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormBuilder</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref25733 title=\"3 occurrences\">#5733</a><samp data-depth=5 id=sf-dump-712697977-ref25733 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">form_builders</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Test Form</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"23 characters\">6891becc855a31754382028</span>\"\n            \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_lead_active</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>form_styles</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>hide_title</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Test Form</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"23 characters\">6891becc855a31754382028</span>\"\n            \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_lead_active</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>form_styles</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>hide_title</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>form_field</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#7609</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormField</span></span> {<a class=sf-dump-ref>#5737</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">form_fields</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Enter your full name</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>29</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Enter your full name</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">placeholder</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormField</span></span> {<a class=sf-dump-ref>#6563</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">form_fields</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>30</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Enter your email address</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>30</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Enter your email address</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">placeholder</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormField</span></span> {<a class=sf-dump-ref>#7048</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">form_fields</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>31</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Country Code</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Select country code</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2813 characters\">[&quot;\\ud83c\\uddee\\ud83c\\uddf3 India (+91)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddf8 United States (+1)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udde7 United Kingdom (+44)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udde6 Canada (+1)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddfa Australia (+61)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddea Germany (+49)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddf7 France (+33)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf9 Italy (+39)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddf8 Spain (+34)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf1 Netherlands (+31)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddea Belgium (+32)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udded Switzerland (+41)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf9 Austria (+43)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddea Sweden (+46)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf4 Norway (+47)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddf0 Denmark (+45)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddee Finland (+358)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf1 Poland (+48)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddff Czech Republic (+420)&quot;,&quot;\\ud83c\\udded\\ud83c\\uddfa Hungary (+36)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddf4 Romania (+40)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddec Bulgaria (+359)&quot;,&quot;\\ud83c\\uddec\\ud83c\\uddf7 Greece (+30)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf9 Portugal (+351)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddea Ireland (+353)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddfa Russia (+7)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\udde6 Ukraine (+380)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\uddf7 Turkey (+90)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf1 Israel (+972)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddea United Arab Emirates (+971)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\udde6 Saudi Arabia (+966)&quot;,&quot;\\ud83c\\uddf6\\ud83c\\udde6 Qatar (+974)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddfc Kuwait (+965)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udded Bahrain (+973)&quot;,&quot;\\ud83c\\uddf4\\ud83c\\uddf2 Oman (+968)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf4 Jordan (+962)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\udde7 Lebanon (+961)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddec Egypt (+20)&quot;,&quot;\\ud83c\\uddff\\ud83c\\udde6 South Africa (+27)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddec Nigeria (+234)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddea Kenya (+254)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udded Ghana (+233)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf3 China (+86)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf5 Japan (+81)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddf7 South Korea (+82)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddec Singapore (+65)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfe Malaysia (+60)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\udded Thailand (+66)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddf3 Vietnam (+84)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\udded Philippines (+63)&quot;,&quot;\\ud83c\\uddee\\ud83c\\udde9 Indonesia (+62)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udde9 Bangladesh (+880)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf0 Pakistan (+92)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\uddf0 Sri Lanka (+94)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf5 Nepal (+977)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf7 Brazil (+55)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfd Mexico (+52)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf7 Argentina (+54)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf1 Chile (+56)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf4 Colombia (+57)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddea Peru (+51)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddea Venezuela (+58)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddfe Uruguay (+598)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddfe Paraguay (+595)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf4 Bolivia (+591)&quot;,&quot;\\ud83c\\uddea\\ud83c\\udde8 Ecuador (+593)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddff New Zealand (+64)&quot;]</span>\"\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>31</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Country Code</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Select country code</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2813 characters\">[&quot;\\ud83c\\uddee\\ud83c\\uddf3 India (+91)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddf8 United States (+1)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udde7 United Kingdom (+44)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udde6 Canada (+1)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddfa Australia (+61)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddea Germany (+49)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddf7 France (+33)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf9 Italy (+39)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddf8 Spain (+34)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf1 Netherlands (+31)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddea Belgium (+32)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udded Switzerland (+41)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf9 Austria (+43)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddea Sweden (+46)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf4 Norway (+47)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddf0 Denmark (+45)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddee Finland (+358)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf1 Poland (+48)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddff Czech Republic (+420)&quot;,&quot;\\ud83c\\udded\\ud83c\\uddfa Hungary (+36)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddf4 Romania (+40)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddec Bulgaria (+359)&quot;,&quot;\\ud83c\\uddec\\ud83c\\uddf7 Greece (+30)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf9 Portugal (+351)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddea Ireland (+353)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddfa Russia (+7)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\udde6 Ukraine (+380)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\uddf7 Turkey (+90)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf1 Israel (+972)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddea United Arab Emirates (+971)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\udde6 Saudi Arabia (+966)&quot;,&quot;\\ud83c\\uddf6\\ud83c\\udde6 Qatar (+974)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddfc Kuwait (+965)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udded Bahrain (+973)&quot;,&quot;\\ud83c\\uddf4\\ud83c\\uddf2 Oman (+968)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf4 Jordan (+962)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\udde7 Lebanon (+961)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddec Egypt (+20)&quot;,&quot;\\ud83c\\uddff\\ud83c\\udde6 South Africa (+27)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddec Nigeria (+234)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddea Kenya (+254)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udded Ghana (+233)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf3 China (+86)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf5 Japan (+81)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddf7 South Korea (+82)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddec Singapore (+65)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfe Malaysia (+60)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\udded Thailand (+66)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddf3 Vietnam (+84)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\udded Philippines (+63)&quot;,&quot;\\ud83c\\uddee\\ud83c\\udde9 Indonesia (+62)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udde9 Bangladesh (+880)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf0 Pakistan (+92)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\uddf0 Sri Lanka (+94)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf5 Nepal (+977)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf7 Brazil (+55)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfd Mexico (+52)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf7 Argentina (+54)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf1 Chile (+56)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf4 Colombia (+57)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddea Peru (+51)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddea Venezuela (+58)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddfe Uruguay (+598)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddfe Paraguay (+595)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf4 Bolivia (+591)&quot;,&quot;\\ud83c\\uddea\\ud83c\\udde8 Ecuador (+593)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddff New Zealand (+64)&quot;]</span>\"\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">placeholder</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n                <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormField</span></span> {<a class=sf-dump-ref>#8842</a><samp data-depth=9 class=sf-dump-compact>\n                  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"11 characters\">form_fields</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>32</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Enter your phone number</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>32</span>\n                    \"<span class=sf-dump-key>form_id</span>\" => <span class=sf-dump-num>7</span>\n                    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n                    \"<span class=sf-dump-key>placeholder</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Enter your phone number</span>\"\n                    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                    \"<span class=sf-dump-key>required</span>\" => <span class=sf-dump-num>1</span>\n                    \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n                    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">required</span>\"\n                    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">placeholder</span>\"\n                    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n                    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                  </samp>]\n                  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                  </samp>]\n                </samp>}\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">form_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">form_styles</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">hide_title</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>customFields</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref28847 title=\"3 occurrences\">#8847</a><samp data-depth=5 id=sf-dump-712697977-ref28847 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#5735</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Gender</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"16 characters\">{{leads.gender}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">radio</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:16:10</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 05:37:47</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[&quot;Male&quot;,&quot;Female&quot;,&quot;Custom&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Gender</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"16 characters\">{{leads.gender}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">radio</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:16:10</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 05:37:47</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"26 characters\">[&quot;Male&quot;,&quot;Female&quot;,&quot;Custom&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#6565</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"17 characters\">{{leads.country}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:53:49</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:53:49</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[&quot;India&quot;,&quot;Bangladesh&quot;,&quot;Nepal&quot;,&quot;Bhutan&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Country</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"17 characters\">{{leads.country}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:53:49</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:53:49</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"39 characters\">[&quot;India&quot;,&quot;Bangladesh&quot;,&quot;Nepal&quot;,&quot;Bhutan&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#7046</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Subject</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"17 characters\">{{leads.subject}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:54:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:54:35</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[&quot;Node Js&quot;,&quot;Python&quot;,&quot;C&quot;,&quot;C++&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Subject</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"17 characters\">{{leads.subject}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:54:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:54:35</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"30 characters\">[&quot;Node Js&quot;,&quot;Python&quot;,&quot;C&quot;,&quot;C++&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#8844</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Photo</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"15 characters\">{{leads.photo}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:55:26</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:55:26</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Photo</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"15 characters\">{{leads.photo}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">file</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:55:26</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:55:26</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#7935</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Link</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"14 characters\">{{leads.link}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">link</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:56:09</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:56:09</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Link</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"14 characters\">{{leads.link}}</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">link</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:56:09</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-02 12:56:09</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#6564</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"15 characters\">name-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"15 characters\">name-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#5736</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"16 characters\">email-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>13</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Email</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"16 characters\">email-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#9732</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>14</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Contact No</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"21 characters\">contact-no-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>14</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Contact No</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"21 characters\">contact-no-1754380711</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 07:58:31</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#7047</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Country Code</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">country-code-1754382028</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2813 characters\">[&quot;\\ud83c\\uddee\\ud83c\\uddf3 India (+91)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddf8 United States (+1)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udde7 United Kingdom (+44)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udde6 Canada (+1)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddfa Australia (+61)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddea Germany (+49)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddf7 France (+33)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf9 Italy (+39)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddf8 Spain (+34)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf1 Netherlands (+31)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddea Belgium (+32)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udded Switzerland (+41)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf9 Austria (+43)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddea Sweden (+46)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf4 Norway (+47)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddf0 Denmark (+45)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddee Finland (+358)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf1 Poland (+48)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddff Czech Republic (+420)&quot;,&quot;\\ud83c\\udded\\ud83c\\uddfa Hungary (+36)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddf4 Romania (+40)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddec Bulgaria (+359)&quot;,&quot;\\ud83c\\uddec\\ud83c\\uddf7 Greece (+30)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf9 Portugal (+351)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddea Ireland (+353)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddfa Russia (+7)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\udde6 Ukraine (+380)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\uddf7 Turkey (+90)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf1 Israel (+972)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddea United Arab Emirates (+971)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\udde6 Saudi Arabia (+966)&quot;,&quot;\\ud83c\\uddf6\\ud83c\\udde6 Qatar (+974)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddfc Kuwait (+965)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udded Bahrain (+973)&quot;,&quot;\\ud83c\\uddf4\\ud83c\\uddf2 Oman (+968)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf4 Jordan (+962)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\udde7 Lebanon (+961)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddec Egypt (+20)&quot;,&quot;\\ud83c\\uddff\\ud83c\\udde6 South Africa (+27)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddec Nigeria (+234)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddea Kenya (+254)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udded Ghana (+233)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf3 China (+86)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf5 Japan (+81)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddf7 South Korea (+82)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddec Singapore (+65)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfe Malaysia (+60)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\udded Thailand (+66)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddf3 Vietnam (+84)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\udded Philippines (+63)&quot;,&quot;\\ud83c\\uddee\\ud83c\\udde9 Indonesia (+62)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udde9 Bangladesh (+880)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf0 Pakistan (+92)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\uddf0 Sri Lanka (+94)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf5 Nepal (+977)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf7 Brazil (+55)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfd Mexico (+52)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf7 Argentina (+54)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf1 Chile (+56)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf4 Colombia (+57)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddea Peru (+51)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddea Venezuela (+58)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddfe Uruguay (+598)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddfe Paraguay (+595)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf4 Bolivia (+591)&quot;,&quot;\\ud83c\\uddea\\ud83c\\udde8 Ecuador (+593)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddff New Zealand (+64)&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>19</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Country Code</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">country-code-1754382028</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">select</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"2813 characters\">[&quot;\\ud83c\\uddee\\ud83c\\uddf3 India (+91)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddf8 United States (+1)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udde7 United Kingdom (+44)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udde6 Canada (+1)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddfa Australia (+61)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddea Germany (+49)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddf7 France (+33)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf9 Italy (+39)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddf8 Spain (+34)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf1 Netherlands (+31)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddea Belgium (+32)&quot;,&quot;\\ud83c\\udde8\\ud83c\\udded Switzerland (+41)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf9 Austria (+43)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddea Sweden (+46)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf4 Norway (+47)&quot;,&quot;\\ud83c\\udde9\\ud83c\\uddf0 Denmark (+45)&quot;,&quot;\\ud83c\\uddeb\\ud83c\\uddee Finland (+358)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf1 Poland (+48)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddff Czech Republic (+420)&quot;,&quot;\\ud83c\\udded\\ud83c\\uddfa Hungary (+36)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddf4 Romania (+40)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddec Bulgaria (+359)&quot;,&quot;\\ud83c\\uddec\\ud83c\\uddf7 Greece (+30)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf9 Portugal (+351)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddea Ireland (+353)&quot;,&quot;\\ud83c\\uddf7\\ud83c\\uddfa Russia (+7)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\udde6 Ukraine (+380)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\uddf7 Turkey (+90)&quot;,&quot;\\ud83c\\uddee\\ud83c\\uddf1 Israel (+972)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddea United Arab Emirates (+971)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\udde6 Saudi Arabia (+966)&quot;,&quot;\\ud83c\\uddf6\\ud83c\\udde6 Qatar (+974)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddfc Kuwait (+965)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udded Bahrain (+973)&quot;,&quot;\\ud83c\\uddf4\\ud83c\\uddf2 Oman (+968)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf4 Jordan (+962)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\udde7 Lebanon (+961)&quot;,&quot;\\ud83c\\uddea\\ud83c\\uddec Egypt (+20)&quot;,&quot;\\ud83c\\uddff\\ud83c\\udde6 South Africa (+27)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddec Nigeria (+234)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddea Kenya (+254)&quot;,&quot;\\ud83c\\uddec\\ud83c\\udded Ghana (+233)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf3 China (+86)&quot;,&quot;\\ud83c\\uddef\\ud83c\\uddf5 Japan (+81)&quot;,&quot;\\ud83c\\uddf0\\ud83c\\uddf7 South Korea (+82)&quot;,&quot;\\ud83c\\uddf8\\ud83c\\uddec Singapore (+65)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfe Malaysia (+60)&quot;,&quot;\\ud83c\\uddf9\\ud83c\\udded Thailand (+66)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddf3 Vietnam (+84)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\udded Philippines (+63)&quot;,&quot;\\ud83c\\uddee\\ud83c\\udde9 Indonesia (+62)&quot;,&quot;\\ud83c\\udde7\\ud83c\\udde9 Bangladesh (+880)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddf0 Pakistan (+92)&quot;,&quot;\\ud83c\\uddf1\\ud83c\\uddf0 Sri Lanka (+94)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddf5 Nepal (+977)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf7 Brazil (+55)&quot;,&quot;\\ud83c\\uddf2\\ud83c\\uddfd Mexico (+52)&quot;,&quot;\\ud83c\\udde6\\ud83c\\uddf7 Argentina (+54)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf1 Chile (+56)&quot;,&quot;\\ud83c\\udde8\\ud83c\\uddf4 Colombia (+57)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddea Peru (+51)&quot;,&quot;\\ud83c\\uddfb\\ud83c\\uddea Venezuela (+58)&quot;,&quot;\\ud83c\\uddfa\\ud83c\\uddfe Uruguay (+598)&quot;,&quot;\\ud83c\\uddf5\\ud83c\\uddfe Paraguay (+595)&quot;,&quot;\\ud83c\\udde7\\ud83c\\uddf4 Bolivia (+591)&quot;,&quot;\\ud83c\\uddea\\ud83c\\udde8 Ecuador (+593)&quot;,&quot;\\ud83c\\uddf3\\ud83c\\uddff New Zealand (+64)&quot;]</span>\"\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\CustomField\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">CustomField</span></span> {<a class=sf-dump-ref>#9170</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">custom_fields</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">phone-number-1754382028</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>20</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Phone Number</span>\"\n                \"<span class=sf-dump-key>unique_key</span>\" => \"<span class=sf-dump-str title=\"23 characters\">phone-number-1754382028</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n                \"<span class=sf-dump-key>module</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Leads</span>\"\n                \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>74</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-05 08:20:28</span>\"\n                \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_required</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => <span class=sf-dump-num>1</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>options</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"6 characters\">module</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">unique_key</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"7 characters\">options</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">is_required</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"116 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\storage\\framework\\views/9862194a0baa09a44222d30f50d0ca4b.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref2330 title=\"3 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref27 title=\"3 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref24337 title=\"3 occurrences\">#4337</a>}\n        \"<span class=sf-dump-key>form</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormBuilder\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormBuilder</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref25733 title=\"3 occurrences\">#5733</a>}\n        \"<span class=sf-dump-key>customFields</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref28847 title=\"3 occurrences\">#8847</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"104 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/form_builder/customize.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref2330 title=\"3 occurrences\">#330</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref27 title=\"3 occurrences\">#7</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref24337 title=\"3 occurrences\">#4337</a>}\n        \"<span class=sf-dump-key>form</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\FormBuilder\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">FormBuilder</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref25733 title=\"3 occurrences\">#5733</a>}\n        \"<span class=sf-dump-key>customFields</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-712697977-ref28847 title=\"3 occurrences\">#8847</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>192</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"27 characters\">app/Http/Middleware/XSS.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>65</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Http\\Middleware\\XSS</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>41</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">app/Http/Middleware/EnsurePermissionsAfterPost.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Middleware\\EnsurePermissionsAfterPost</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"37 characters\">app/Http/Middleware/FilterRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Http\\Middleware\\FilterRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>76</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>77</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>78</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"72 characters\">C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-712697977\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["                                                                   stripos($field->name, 'mobile') !== false;\r\n", "\r\n", "                                                    // Check if there's already a separate Country Code field in this form\r\n", "                                                    $hasCountryCodeField = $formbuilder->form_field->contains(function($formField) {\r\n", "                                                        return stripos($formField->name, 'country code') !== false ||\r\n", "                                                               stripos($formField->name, 'country_code') !== false;\r\n", "                                                    });\r\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fstorage%2Fframework%2Fviews%2F9862194a0baa09a44222d30f50d0ca4b.php&line=4141", "ajax": false, "filename": "9862194a0baa09a44222d30f50d0ca4b.php", "line": "4141"}}]}}